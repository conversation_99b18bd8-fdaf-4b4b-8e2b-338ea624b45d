<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model.trim="searchForm.keyWords" placeholder="出库单号/申请人" clearable style="width: 200px"></el-input>
        <el-select v-model.trim="searchForm.warehouseId" filterable placeholder="出库仓库">
          <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"> </el-option>
        </el-select>
        <el-select v-model="searchForm.outwarehouseType" placeholder="出库类型" class="ml-16" clearable filterable>
          <el-option v-for="item in outboundTypeList" :key="item.dictionaryDetailsId" :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId"> </el-option>
        </el-select>
        <el-select v-model="searchForm.status" placeholder="出库状态" class="ml-16" clearable filterable>
          <el-option v-for="item in outwarehouseStatusList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
        <el-select v-model="searchForm.applicantDepartmentId" placeholder="申请人部门" class="ml-16" clearable filterable>
          <el-option v-for="item in deptOptions" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
        </el-select>
        <el-date-picker v-model="searchDate" type="daterange" start-placeholder="申请开始日期" range-separator="至" end-placeholder="申请结束日期"> </el-date-picker>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div class="btn-group">
        <el-button type="primary" @click="handleListEvent('add')">新增出库</el-button>
        <el-button :disabled="selectTableList.length == 0" type="danger" @click="handleListEvent('del')">删除</el-button>
        <el-button type="primary" plain @click="handleListEvent('export')">导出</el-button>
      </div>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px - 50px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @selection-change="handleSelectionChange"
      ></TablePage>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'
import moment from 'moment'
import axios from 'axios'
export default {
  name: 'outWarehouseManage',
  data() {
    return {
      moment,
      tableLoading: false,
      searchForm: {
        keyWords: '',
        outwarehouseType: '',
        status: '',
        beginDate: '',
        endDate: '',
        applicantDepartmentId: '',
        warehouseId: ''
      },
      warehouseList: [],
      searchDate: [],
      selectTableList: '',
      tableColumn: [
        {
          type: 'selection',
          width: 55,
          align: 'center'
        },
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.currentPage - 1) * this.pageData.size + scope.$index + 1
          },
          width: 80
        },
        {
          prop: 'recordNumber',
          label: '出库单号',
          required: true,
          formatter: (scope) => {
            return scope.row.status === '待提交' ? '' : scope.row.recordNumber
          }
        },
        {
          prop: 'warehouseName',
          label: '出库仓库'
        },
        {
          prop: 'outwarehouseTypeName',
          label: '出库类型'
        },
        {
          prop: 'createTime',
          label: '申请时间'
        },
        {
          prop: 'applicantName',
          label: '申请人'
        },
        {
          prop: 'applicantDepartmentName',
          label: '申请人部门'
        },
        {
          prop: 'outwarehouseDate',
          label: '出库日期',
          formatter: (scope) => {
            return moment(scope.row.outwarehouseDate).format('YYYY-MM-DD ')
          }
        },
        {
          prop: 'status',
          label: '出库状态'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 180,
          render: (h, row) => {
            // 权限校验
            const currentUserId = this.userInfo?.staffId
            const canEdit = row.row.createCode === currentUserId // 创建人可编辑
            const canReview = row.row.warehouseManagerId === currentUserId // 仓库管理员可审核
            // console.log('权限校验信息:', {
            //   currentUserId,
            //   createCode: row.row.createCode,
            //   warehouseManagerId: row.row.warehouseManagerId,
            //   canEdit,
            //   canReview,
            //   status: row.row.status
            // })
            return (
              <div className="operationBtn">
                <span className="operationBtn-span" style="color: #3562db; margin-right: 8px; cursor: pointer" onClick={() => this.handleListEvent('detail', row.row)}>
                  详情
                </span>
                {row.row.status === '待提交' && (
                  <span
                    className="operationBtn-span"
                    style={{
                      color: canEdit ? '#3562db' : '#ccc',
                      marginRight: '8px',
                      cursor: canEdit ? 'pointer' : 'not-allowed'
                    }}
                    onClick={() => canEdit && this.handleListEvent('edit', row.row)}
                    title={canEdit ? '' : '只有创建人可以编辑'}
                  >
                    编辑
                  </span>
                )}
                {(row.row.status === '待审核' || row.row.status === '已挂起') && (
                  <span
                    className="operationBtn-span"
                    style={{
                      color: canReview ? '#3562db' : '#ccc',
                      cursor: canReview ? 'pointer' : 'not-allowed'
                    }}
                    onClick={() => canReview && this.handleListEvent('review', row.row)}
                    title={canReview ? '' : '只有仓库管理员可以审核'}
                  >
                    审核
                  </span>
                )}
              </div>
            )
          }
        }
      ],
      pageProps: {
        page: 'currentPage',
        pageSize: 'size',
        total: 'total'
      },
      tableData: [],
      userInfo: {},
      pageData: {
        currentPage: 1,
        size: 15,
        total: 0
      },
      outboundTypeList: [],
      outwarehouseStatusList: [
        {
          id: '1',
          name: '待提交'
        },
        {
          id: '2',
          name: '待审核'
        },
        {
          id: '4',
          name: '审核驳回'
        },
        {
          id: '5',
          name: '已完成'
        }
      ],
      deptOptions: []
    }
  },
  created() {
    this.userInfo = this.$store.state.user.userInfo.user
  },
  mounted() {
    this.getWarehouseList()
    this.getDeptList()
    this.getWarehouseType()
    this.getList()
  },
  methods: {
    // 获取仓库列表
    getWarehouseList() {
      let param = {
        pageSize: 99999,
        CurrentPage: 1,
        status: '0'
      }
      this.$api.warehouseApi.getWarehouseByPage(param).then((res) => {
        if (res.code == 200) {
          this.warehouseList = res.data.list
        }
      })
    },
    // 获取部门
    getDeptList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          this.deptOptions = res.data
        }
      })
    },
    // 获取出库类型
    getWarehouseType() {
      const params = {
        pageSize: 99999,
        currentPage: 1,
        userType: 1,
        userId: this.userInfo.staffId,
        userName: this.userInfo.staffName,
        dictionaryCategoryId: 'outbound_type',
        dictionaryDetailsStatus: '1'
      }
      this.$api.warehouseApi.getDictByPage(params).then((res) => {
        if (res.code == '200') {
          this.outboundTypeList = res.data
        } else {
          throw res.msg || res.message
        }
      })
    },
    // 表格多选
    handleSelectionChange(val) {
      this.selectTableList = val
    },
    handleListEvent(type, row) {
      switch (type) {
        case 'add':
          this.$router.push({ name: 'outWarehouseManageAdd', query: { id: '', type: type, recordNumber: '' } })
          break
        case 'edit':
          // 权限校验：只有创建人可以编辑
          if (row && row.createCode !== this.userInfo.staffId) {
            this.$message.warning('您没有权限编辑此出库单，只有创建人可以编辑')
            return
          }
          this.$router.push({ name: 'outWarehouseManageAdd', query: { id: row ? row.id : '', type: type, recordNumber: row ? row.recordNumber : '' } })
          break
        case 'export':
          this.handleExport()
          break
        case 'detail':
          this.$router.push({ name: 'outWarehouseManageDetails', query: { id: row.id, type: type, recordNumber: row.recordNumber } })
          break
        case 'review':
          // 权限校验：只有仓库管理员可以审核
          if (row && row.warehouseManagerId !== this.userInfo.staffId) {
            this.$message.warning('您没有权限审核此出库单，只有仓库管理员可以审核')
            return
          }
          this.$router.push({ name: 'outWarehouseManageAdd', query: { id: row.id, type: type, recordNumber: row.recordNumber } })
          break
        case 'del':
          // 筛选出待提交状态的单子
          const deletableItems = this.selectTableList.filter((item) => item.status === '待提交')

          if (deletableItems.length === 0) {
            this.$message.warning('所选数据中没有可删除的单据（只能删除待提交状态的单据）')
            return
          }

          const totalSelected = this.selectTableList.length
          const deletableCount = deletableItems.length
          const nonDeletableCount = totalSelected - deletableCount

          let confirmMessage = `确定要删除选中的 ${deletableCount} 条待提交状态的单据吗？`
          if (nonDeletableCount > 0) {
            confirmMessage += `\n注意：已选中的 ${nonDeletableCount} 条非待提交状态的单据将不会被删除。`
          }

          this.$confirm(confirmMessage, '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const params = {
              userId: this.userInfo.staffId,
              userName: this.userInfo.staffName,
              recordNumber: deletableItems.map((item) => item.recordNumber).join(',')
            }
            this.$api.warehouseApi.getWarehouseOutputdelete(params).then((res) => {
              if (res.code === '200') {
                this.$message.success(`删除成功，共删除 ${deletableCount} 条记录`)
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          })
      }
    },
    // 导出方法
    handleExport() {
      const param = {
        ...this.searchForm,
        userId: this.userInfo.staffId,
        userName: this.userInfo.staffName,
        hospitalCode: this.userInfo.hospitalCode,
        unitCode: this.userInfo.unitCode
      }

      // 如果有选中的数据，则只导出选中的数据
      if (this.selectTableList && this.selectTableList.length > 0) {
        // 获取选中的出库单号，用逗号分隔
        param.recordNumber = this.selectTableList
          .map((item) => item.recordNumber)
          .filter(Boolean)
          .join(',')
      }

      // 使用axios直接请求二进制流
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'outwarehouseRecord/exportOutwarehouseList',
        data: param,
        responseType: 'blob', // 指定响应类型为blob
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'Content-Type': 'application/json'
        },
        transformRequest: [
          (data) => {
            let formData = new FormData()
            for (let key in data) {
              formData.append(key, data[key] || '')
            }
            return formData
          }
        ]
      })
        .then((res) => {
          // 检查返回的内容类型
          const contentType = res.headers['content-type']
          if (contentType && contentType.includes('application/json')) {
            // 如果返回的是JSON（可能是错误信息），则读取并显示
            const reader = new FileReader()
            reader.onload = () => {
              try {
                const result = JSON.parse(reader.result)
                this.$message.error('导出失败：' + (result.msg || '未知错误'))
              } catch (e) {
                this.$message.error('导出失败：未知错误')
              }
            }
            reader.readAsText(res.data)
          } else {
            // 创建Blob对象并下载
            const blob = new Blob([res.data], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
            // 使用file-saver保存文件
            saveAs(blob, '出库记录.xlsx')
            this.$message.success('导出成功')
          }
        })
        .catch((error) => {
          this.$message.error('导出失败：' + (error.message || error))
        })
    },
    // 查询
    search() {
      if (this.searchDate && this.searchDate.length === 2) {
        const start = new Date(this.searchDate[0])
        const end = new Date(this.searchDate[1])
        // 格式化为本地时间字符串（YYYY-MM-DD 00:00:00 和 23:59:59）
        const pad = (n) => n.toString().padStart(2, '0')
        this.searchForm.beginDate = `${start.getFullYear()}-${pad(start.getMonth() + 1)}-${pad(start.getDate())} 00:00:00`
        this.searchForm.endDate = `${end.getFullYear()}-${pad(end.getMonth() + 1)}-${pad(end.getDate())} 23:59:59`
      } else {
        this.searchForm.beginDate = ''
        this.searchForm.endDate = ''
      }
      this.getList()
    },
    // 重置查询
    resetForm() {
      this.searchForm = {
        keyWords: '',
        outwarehouseType: '',
        status: '',
        beginDate: '',
        endDate: ''
      }
      this.searchDate = []
      this.pageData.page = 1
      this.getList()
    },
    // 获取应用列表
    getList() {
      let param = {
        ...this.searchForm,
        pageSize: this.pageData.size,
        currentPage: this.pageData.currentPage,
        userId: this.userInfo.staffId,
        userName: this.userInfo.staffName
      }
      this.tableLoading = true
      this.$api.warehouseApi
        .getOutwarehouseRecordList(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.pageData.total = res.data.sum
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.control-btn-header {
  padding: 10px !important;
  .search-from {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;

    .el-input,
    .el-select,
    .el-date-picker {
      margin-bottom: 8px;
    }

    & > div {
      margin-right: 0;
      margin-bottom: 8px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  .btn-group {
    height: 50px;
  }
}
</style>
