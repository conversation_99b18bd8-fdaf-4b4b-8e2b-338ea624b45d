<template>
  <PageContainer class="inOutRecord-detail" :footer="false">
    <template #content>
      <div class="inOutRecord-content-title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i> 出入库记录详情</div>
      <div class="inOutRecord-detail__content">
        <div class="inOutRecord-detail__header">
          <el-form ref="formRef" :model="searchForm" inline>
            <el-form-item prop="type">
              <el-select v-model="searchForm.type" placeholder="类型">
                <el-option v-for="item of typeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="orderNumber">
              <el-input v-model="searchForm.orderNumber" placeholder="单号" clearable></el-input>
            </el-form-item>
            <el-form-item prop="time">
              <el-date-picker
                v-model="searchForm.time"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div>
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">搜索</el-button>
          </div>
        </div>
        <div class="inOutRecord-detail__actions">
          <el-button type="primary" plain :exportLoading="exportLoading" @click="handlerExport">导出</el-button>
        </div>
        <div class="inOutRecord-detail__statistics">
          <span>
            <span>库存总数</span>
            <span class="text-red">{{ totalNum }}</span>
          </span>
          <span class="price">
            <span>金额（元）</span>
            <span class="text-red">{{ totalPrice }}</span>
          </span>
        </div>
        <div class="inOutRecord-detail__table">
          <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
            <el-table-column label="类型" prop="type"> </el-table-column>
            <el-table-column label="单号" prop="orderNumber">
              <template #default="{ row }">
                <span style="color: rgb(0, 138, 244); cursor: pointer" @click="toDetail(row)">{{ row.orderNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="出/入库日期" prop="createTime" show-overflow-tooltip></el-table-column>
            <el-table-column label="数量" prop="operateCount" show-overflow-tooltip> </el-table-column>
            <el-table-column label="单价（元）" prop="unitPriceStr" show-overflow-tooltip> </el-table-column>
            <el-table-column label="金额（元）" prop="sumAmountStr" show-overflow-tooltip> </el-table-column>
          </el-table>
          <el-pagination
            class="inOutRecord-detail__pagination"
            :current-page="pagination.page"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </template>
    <div slot="footer">
      <el-button type="primary" plain @click="onClose">返回</el-button>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import axios from 'axios'
export default {
  name: 'inOutRecord',
  mixins: [tableListMixin],
  data: () => ({
    tableData: [],
    tableLoadingStatus: false,
    searchForm: {
      orderNumber: '', // 单号
      type: null, // 出库类型
      time: []
    },
    totalNum: 0,
    totalPrice: 0,
    // 类型选项
    typeOptions: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '出库',
        value: 'WZCK'
      },
      {
        label: '入库',
        value: 'WZRK'
      }
    ],
    exportLoading: false
  }),
  mounted() {
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const { orderNumber, type, time } = this.searchForm
      const params = {
        pageSize: this.pagination.size,
        CurrentPage: this.pagination.current,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        orderNumber,
        type,
        beginDate: time.length ? time[0] : '',
        endDate: time.length ? time[1] : '',
        warehouseId: this.$route.query.warehouseId,
        materialCode: this.$route.query.materialCode
      }
      this.$api.warehouseApi
        .getInOutRecordListByPage(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
            this.totalNum = res.data.operateCount || 0
            this.totalPrice = res.data.sumAmount || 0
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.currentKey = ''
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 导出
    handlerExport() {
      this.exportLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      const { orderNumber, type, time } = this.searchForm
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        orderNumber,
        type,
        beginDate: time.length ? time[0] : '',
        endDate: time.length ? time[1] : '',
        warehouseId: this.$route.query.warehouseId,
        materialCode: this.$route.query.materialCode
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'inventoryManage/exportInOutRecordList',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          this.$message.success(res.message || '导出成功')
          this.exportLoading = false
        })
        .catch(() => {
          this.$message.error('导出失败')
          this.exportLoading = false
        })
    },
    // 关闭
    onClose() {
      this.$router.go(-1)
    },
    toDetail(row) {
      this.$router.push({ name: 'inWarehouseManageDetails', query: { type: 'detail', recordNumber: row.orderNumber } })
    }
  }
}
</script>
<style scoped lang="scss">
.inOutRecord-detail {
  &__content {
    padding: 16px;
    height: calc(100% - 40px);
    overflow: hidden;
  }
  &__header {
    display: flex;
    justify-content: space-between;
  }
  .el-form-item {
    margin-bottom: 16px;
  }
  &__table {
    margin-top: 16px;
    height: calc(100% - 200px);
  }
  &__pagination {
    margin-top: 16px;
  }
  &__statistics {
    margin-top: 16px;
    .price {
      margin-left: 16px;
    }
    .text-red {
      color: red;
      font-weight: bold;
      margin-left: 8px;
    }
  }
  ::v-deep(.container-content) {
    padding-top: 2px;
    margin-top: 10px;
    background: #fff;
    height: 100%;
  }
}
.inOutRecord-content-title {
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 16px;
  cursor: pointer;
}
</style>
