<template>
  <PageContainer v-loading="pageLoading" class="warehouse-detail" :footer="false">
    <template #content>
      <div class="warehouse-content-title">
        <span @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          <span>仓库详情</span>
        </span>
      </div>
      <el-tabs v-model="currentTab">
        <el-tab-pane label="基础信息" name="WarehouseDetail"></el-tab-pane>
        <el-tab-pane label="库存请单" name="InventoryList"></el-tab-pane>
        <el-tab-pane label="出入库记录" name="InboundRecords"></el-tab-pane>
      </el-tabs>
      <div class="warehouse-detail__content">
        <keep-alive>
          <component :is="currentTab" :id="warehouseId" :baseData="baseData" style="padding: 0 16px 0 0" large></component>
        </keep-alive>
      </div>
    </template>
    <div slot="footer">
      <el-button type="primary" plain @click="onClose">返回</el-button>
    </div>
  </PageContainer>
</template>
<script>
import WarehouseDetail from './components/WarehouseDetail.vue'
export default {
  name: 'warehouseDetail',
  components: {
    WarehouseDetail,
    InventoryList: () => import('./components/InventoryList.vue'),
    InboundRecords: () => import('./components/InboundRecords.vue')
  },
  data: () => ({
    currentTab: 'WarehouseDetail',
    // 仓库信息
    baseData: null,
    pageLoading: false,
    warehouseId: ''
  }),
  computed: {
    isFormTab() {
      return this.currentTab === 'WarehouseDetail'
    }
  },
  mounted() {
    this.warehouseId = this.$route.query.id
    // 添加类型映射逻辑
    if (this.$route.query.type) {
      const typeMap = {
        detail: 'WarehouseDetail',
        inventory: 'InventoryList',
        inventoryRecord: 'InboundRecords'
      }

      const mappedTab = typeMap[this.$route.query.type]
      if (mappedTab) {
        this.currentTab = mappedTab
      } else {
        // 如果没有匹配的映射，保持默认值
        this.currentTab = 'WarehouseDetail'
      }
    }

    if (this.warehouseId) {
      this.getWarehouseInfo()
    }
  },
  methods: {
    /**
     * 获取仓库详情
     */
    getWarehouseInfo() {
      this.pageLoading = true
      this.$api.warehouseApi
        .getWarehouseById({ id: this.warehouseId })
        .then((res) => {
          if (res.code === '200') {
            this.baseData = res.data
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取仓库详情失败'))
        .finally(() => (this.pageLoading = false))
    },
    // 关闭
    onClose() {
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped lang="scss">
.warehouse-detail {
  &__content {
    padding: 16px 0 16px 16px;
    height: calc(100% - 40px);
    overflow: hidden;
    .warehouse-form {
      height: 100%;
      overflow: auto;
    }
  }
  ::v-deep(.container-content) {
    padding-top: 2px;
    margin-top: 10px;
    background: #fff;
  }
  ::v-deep(.el-tabs__nav-scroll) {
    padding-left: 16px;
  }
}
.warehouse-content-title {
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 16px;
  cursor: pointer;
}
</style>
