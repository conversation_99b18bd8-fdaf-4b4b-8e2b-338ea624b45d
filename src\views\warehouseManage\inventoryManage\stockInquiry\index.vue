<template>
  <PageContainer class="stockInquiry-list">
    <template #content>
      <div class="stockInquiry-list__left">
        <div class="toptip">
          <div><span class="green_line"></span> 仓库</div>
        </div>
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="treeData"
          :current-node-key="currentKey"
          :expand-on-click-node="false"
          default-expand-all
          highlight-current
          :props="{ label: 'warehouseName' }"
          class="dictionary-list__tree"
          @node-click="onTreeNodeClick"
        ></el-tree>
      </div>
      <div class="stockInquiry-list__right">
        <div class="stockInquiry-list__right__header">
          <el-form ref="formRef" :model="searchForm" class="stockInquiry-list__search" inline @submit.native.prevent="onSearch">
            <el-form-item prop="keyWords">
              <el-input v-model="searchForm.keyWords" clearable filterable placeholder="耗材名称/耗材编码"></el-input>
            </el-form-item>
            <el-form-item prop="materialTypeCode">
              <el-cascader
                v-model="searchForm.materialTypeCode"
                :options="consumablesTypeList"
                :props="defaultProps"
                clearable
                :show-all-levels="false"
                placeholder="耗材类型"
              ></el-cascader>
            </el-form-item>
          </el-form>
          <div class="stockInquiry-list__right__actions">
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">搜索</el-button>
          </div>
        </div>
        <div class="stockInquiry-list__right__operations">
          <el-button type="primary" plain @click="handlerExport">导出</el-button>
        </div>
        <div class="stockInquiry-list__statistics">
          <span>
            <span>库存总数</span>
            <span class="text-red">{{ totalNum }}</span>
          </span>
          <span class="price">
            <span>金额（元）</span>
            <span class="text-red">{{ totalPrice }}</span>
          </span>
        </div>
        <div class="stockInquiry-list__table">
          <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
            <el-table-column label="序号" type="index" width="80">
              <template slot-scope="scope">
                <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="仓库名称" prop="warehouseName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="耗材编码" prop="materialCode" show-overflow-tooltip> </el-table-column>
            <el-table-column label="耗材名称" prop="materialName" show-overflow-tooltip></el-table-column>
            <el-table-column label="耗材类型" prop="materialTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="规格型号" prop="model" show-overflow-tooltip> </el-table-column>
            <el-table-column label="计量单位" prop="basicUnitName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="品牌" prop="brandName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="当前库存" prop="num" show-overflow-tooltip> </el-table-column>
            <el-table-column label="单价（元）" prop="unitPriceStr" show-overflow-tooltip> </el-table-column>
            <el-table-column label="金额（元）" prop="sumAmountStr" show-overflow-tooltip> </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="text" @click="control('detail', row)"> 详情 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            class="stockInquiry-list__pagination"
            :current-page="pagination.page"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin'
import axios from 'axios'
export default {
  name: 'stockInquiry',
  mixins: [tableListMixin],
  data() {
    return {
      currentKey: '',
      searchForm: {
        keyWords: '',
        materialTypeCode: ''
      },
      treeData: [],
      treeLoadingStatus: false,
      tableData: [],
      tableLoadingStatus: false,
      consumablesTypeList: [],
      defaultProps: {
        label: 'dictionaryDetailsName',
        children: 'children',
        value: 'id',
        checkStrictly: true
      },
      totalNum: 0,
      totalPrice: 0,
      exportLoading: false
    }
  },
  mounted() {
    this.getConsumableTypeList()
    this.getTreeData()
    this.getDataList()
  },
  methods: {
    // 查询耗材类型
    getConsumableTypeList() {
      this.$api.warehouseApi.getDictByPage({ dictionaryCategoryId: 'consumables_type', dictionaryDetailsStatus: '1' }).then((res) => {
        if (res.code == '200') {
          this.consumablesTypeList = transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 获取树数据
    getTreeData() {
      this.treeLoadingStatus = true
      let param = {
        status: '0'
      }
      this.$api.warehouseApi
        .getWarehouseByPage(param)
        .then((res) => {
          if (res.code == 200) {
            // 创建root节点，固定
            const root = { warehouseName: '全部', id: '', children: [] }
            root.children = res.data.list.map((it) => {
              // 设置父级ID
              it.parentId = root.id
              return it
            })
            this.treeData = [root]
            // 当树数据加载完成后，默认选中第一个节点
            const first = root
            this.currentKey = first.id
            this.onSearch()
            this.$nextTick(() => {
              this.$refs.treeRef.setCurrentKey(first.id)
            })
          }
          this.treeLoadingStatus = false
        })
        .catch((msg) => this.$message.error(msg || '获取仓库失败'))
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    // 当树点击被点击时
    onTreeNodeClick(data) {
      if (data.id !== this.currentKey) {
        this.currentKey = data.id
        this.onSearch()
      }
    },
    // 详情
    control(type, row) {
      if (type === 'detail') {
        let query = { warehouseId: row.warehouseId, materialCode: row.materialCode }
        this.$router.push({
          path: '/inventoryManage/stockInquiry/inOutRecord',
          query: {
            ...query
          }
        })
      }
    },
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const { materialTypeCode, keyWords } = this.searchForm
      const params = {
        pageSize: this.pagination.size,
        CurrentPage: this.pagination.current,
        warehouseId: this.currentKey,
        keyWords,
        materialTypeCode: Array.isArray(materialTypeCode) ? materialTypeCode[materialTypeCode.length - 1] : ''
      }
      this.$api.warehouseApi
        .getInventoryListByPage(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
            this.totalNum = res.data.sumCount || 0
            this.totalPrice = res.data.sumAmount || 0
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.currentKey = ''
      this.$refs.formRef.resetFields()
      this.$nextTick(() => {
        this.$refs.treeRef.setCurrentKey(this.currentKey)
      })
      this.onSearch()
    },
    // 导出
    handlerExport() {
      this.exportLoading = true
      const { materialTypeCode, keyWords } = this.searchForm
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        warehouseId: this.currentKey,
        keyWords,
        materialTypeCode: Array.isArray(materialTypeCode) ? materialTypeCode[materialTypeCode.length - 1] : ''
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'inventoryManage/exportInventoryList',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          this.$message.success(res.message || '导出成功')
          this.exportLoading = false
        })
        .catch(() => {
          this.$message.error('导出失败')
          this.exportLoading = false
        })
    }
  }
}
</script>
<style scoped lang="scss">
.stockInquiry-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    display: flex;
    flex-flow: column nowrap;
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      padding: 16px;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
    .el-form-item {
      margin-bottom: 16px;
    }
    &__operations {
      margin-bottom: 16px;
    }
  }
  &__right__header {
    display: flex;
    justify-content: space-between;
  }
  &__right__actions {
    line-height: 40px;
  }
  &__table {
    height: calc(100% - 190px);
  }
  &__pagination {
    margin-top: 16px;
  }
  &__statistics {
    margin-bottom: 16px;
    .price {
      margin-left: 16px;
    }
    .text-red {
      color: red;
      font-weight: bold;
      margin-left: 8px;
    }
  }
}
.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 6px;
  border-radius: 2px !important;
  vertical-align: middle;
}
.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  padding-left: 16px;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}
</style>
