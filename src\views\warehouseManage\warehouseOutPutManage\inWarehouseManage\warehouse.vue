<template>
  <div class="main">
    <el-dialog width="80%" title="关联出库单" :visible.sync="warehouseVisible" :before-close="closeDialog" append-to-body custom-class="model-dialog">
      <div class="outermost">
        <div v-loading="loading" class="content">
          <div class="topTools">
            <el-input v-model.trim="searchForm.keyWords" placeholder="出库单号/工单号/申请人" style="width: 200px; margin-right: 10px"></el-input>
            <el-select v-model="searchForm.warehouseId" placeholder="仓库" style="width: 150px; margin-right: 10px" clearable filterable>
              <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"> </el-option>
            </el-select>
            <el-select v-model="searchForm.outwarehouseType" placeholder="出库类型" style="width: 150px; margin-right: 10px" clearable filterable>
              <el-option v-for="item in inboundTypeList" :key="item.dictionaryDetailsId" :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId"> </el-option>
            </el-select>
            <el-select v-model="searchForm.applicantDepartmentId" style="width: 150px; margin-right: 10px" placeholder="申请部门" clearable filterable>
              <el-option v-for="(item, index) in sourcesDeptOptions" :key="index" :label="item.officeName" :value="item.id"> </el-option>
            </el-select>
            <el-date-picker
              v-model="timeRange"
              style="margin-right: 10px"
              type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始时间"
              range-separator="至"
              end-placeholder="结束时间"
              @change="timeRangeChange"
            >
            </el-date-picker>
            <el-button type="primary" @click="reset">重 置</el-button>
            <el-button type="primary" @click="search">查 询</el-button>
          </div>
          <el-table
            ref="personTable"
            v-loading="tableLoading"
            :data="tableData"
            border
            :height="tableHeight"
            stripe
            class="single-select-table"
            :row-key="getRowKeys"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column type="index" label="序号" width="75">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="出库单号" prop="recordNumber" show-overflow-tooltip>
              <template slot-scope="scope">
                <span style="color: #5c81e3">{{ scope.row.recordNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="工单号" prop="workNum" show-overflow-tooltip>
              <template slot-scope="scope">
                <span style="color: #5c81e3">{{ scope.row.workNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="出库类型" prop="outwarehouseTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="出库仓库" prop="warehouseName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="申请人" prop="applicantName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="申请人部门" prop="applicantDepartmentName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="出库日期" prop="outwarehouseDate" show-overflow-tooltip> </el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-pagination
          class="user-pagination"
          style
          :current-page="currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :pager-count="5"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
        <el-button type="primary" @click="closeDialog">取 消</el-button>
        <el-button type="primary" :disabled="addDis" class="sino-button-sure" @click="editSubmit">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    warehouseVisible: {
      type: Boolean,
      default: false
    },
    tableDate: {}
  },
  data() {
    return {
      inboundTypeList: [],
      typeList: [],
      searchForm: {
        keyWords: '',
        warehouseId: '',
        outwarehouseType: '',
        applicantDepartmentId: '',
        beginDate: '',
        endDate: ''
      },
      warehouseList: [],
      timeRange: [],
      filterText: '',
      total: 0,
      currentPage: 1,
      treeDataTop: '',
      data: [],
      pageSize: 10,
      tableLoading: false,
      treeLoading: false,
      multipleSelection: [],
      tableData: [],
      loading: false,
      addDis: false,
      userInfo: {},
      sourcesDeptOptions: [] // 申报科室
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 525
    }
  },
  watch: {},
  created() {
    this.userInfo = this.$store.state.user.userInfo.user
  },
  mounted() {
    this.getOfficeList()
    this.getWarehouseType()
    this.search()
    this.getWarehouseList()
  },
  methods: {
    getWarehouseList() {
      let param = {
        pageSize: 99999,
        CurrentPage: 1,
        status: '0'
      }
      this.$api.warehouseApi.getWarehouseByPage(param).then((res) => {
        if (res.code == 200) {
          this.warehouseList = res.data.list
        }
      })
    },
    // 日期改变
    timeRangeChange(val) {
      if (val && val.length) {
        this.searchForm.beginDate = val[0] // 开始时间
        this.searchForm.endDate = val[1] // 结束时间
      } else {
        this.searchForm.beginDate = ''
        this.searchForm.endDate = ''
      }
    },
    // 获取申报科室
    getOfficeList() {
      this.$api.warehouseApi.getOfficeAll().then((res) => {
        if (res) {
          this.sourcesDeptOptions = res.body.data
        }
      })
    },
    // 获取出库类型
    getWarehouseType() {
      const params = {
        pageSize: 99999,
        currentPage: 1,
        userType: 1,
        userId: this.userInfo.staffId,
        userName: this.userInfo.staffName,
        dictionaryCategoryId: 'outbound_type',
        dictionaryDetailsStatus: '1'
      }
      this.$api.warehouseApi.getDictByPage(params).then((res) => {
        if (res.code == '200') {
          this.inboundTypeList = res.data
        } else {
          throw res.msg || res.message
        }
      })
    },
    getRowKeys(row) {
      return row.id
    },
    closeDialog() {
      this.$emit('closeWarehouseDialog')
    },
    editSubmit() {
      if (this.multipleSelection.length > 0) {
        this.$emit('handlewarehouse', this.multipleSelection)
      } else {
        this.$message.error('请先选择出库单')
      }
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.search()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.search()
    },
    handleNodeClick(data) {
      this.currentPage = 1
      this.treeDataTop = data.id
      this.search()
    },
    handleSelectionChange(val) {
      // 单选处理逻辑
      if (val.length > 1) {
        this.$refs.personTable.clearSelection() // 清空所有选择
        this.$refs.personTable.toggleRowSelection(val.pop()) // 保留最后一个选中项
        return
      }
      this.multipleSelection = val
    },
    search() {
      if (this.timeRange && this.timeRange.length === 2) {
        const start = new Date(this.timeRange[0])
        const end = new Date(this.timeRange[1])
        // 格式化为本地时间字符串（YYYY-MM-DD 00:00:00 和 23:59:59）
        const pad = (n) => n.toString().padStart(2, '0')
        this.searchForm.beginDate = `${start.getFullYear()}-${pad(start.getMonth() + 1)}-${pad(start.getDate())} 00:00:00`
        this.searchForm.endDate = `${end.getFullYear()}-${pad(end.getMonth() + 1)}-${pad(end.getDate())} 23:59:59`
      } else {
        this.searchForm.beginDate = ''
        this.searchForm.endDate = ''
      }
      this.getList()
    },
    getList() {
      let params = {
        ...this.searchForm,
        pageSize: this.pageSize,
        currentPage: this.currentPage,
        userId: this.userInfo.staffId,
        userName: this.userInfo.staffName
      }
      this.tableLoading = true
      this.$api.warehouseApi.getOutwarehouseRecordList(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.sum
        }
      })
    },
    reset() {
      this.currentPage = 1
      this.searchForm = {
        keyWords: '',
        warehouseId: '',
        outwarehouseType: '',
        applicantDepartmentId: '',
        beginDate: '',
        endDate: ''
      }
      this.timeRange = []
      this.search()
    }
  }
}
</script>
<style lang="scss" scoped>
.outermost {
  display: flex;
  width: 100%;
  height: 500px;
  border: 1px solid #eee;
  padding: 10px;
}
.content {
  background-color: #fff;
  padding: 10px;
  width: 100%;
  height: 100%;
}
.topTools {
  margin-bottom: 5px;
}
.user-pagination {
  text-align: right;
  position: absolute;
  right: 250px;
  bottom: 10px;
}
// @media screen and(max-width: 1600px) {
//   .user-pagination {
//     right: 210px;
//     bottom: 10px;
//   }
// }
.tree-title {
  border: 1px solid #ebeef5;
  width: 40%;
  margin-bottom: 5px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  color: #5188fc;
  cursor: pointer;
}
.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}
.block {
  height: calc(100% - 80px);
  overflow: auto;
}
::v-deep .el-tree-node__label {
  display: inline-block;
  white-space: normal;
  text-align: left;
}
::v-deep .el-tree-node__content {
  height: auto;
}
::v-deep .single-select-table {
  .el-table__header-wrapper .el-checkbox {
    display: none;
  }
}
</style>
