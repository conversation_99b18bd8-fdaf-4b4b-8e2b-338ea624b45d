<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <div class="left">
          <el-input v-model="searchForm.keyWords" placeholder="仓库名称/库管员" clearable style="width: 200px"></el-input>
          <el-cascader
            v-model="searchForm.manageUnitId"
            placeholder="管理部门"
            :options="managerUnitOptions"
            :props="manageUnitProps"
            :show-all-levels="false"
            clearable
            filterable
          >
          </el-cascader>
          <el-cascader v-model="searchForm.useUnitId" placeholder="使用部门" :options="useUnitOptions" :props="useUnitProps" :show-all-levels="false" clearable filterable>
          </el-cascader>
          <el-select v-model="searchForm.status" placeholder="仓库状态">
            <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </div>
        <div class="right">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">搜索</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div class="btn-list">
        <el-button type="primary" icon="el-icon-plus" @click="control('add')">添加</el-button>
        <el-button type="primary" plain :disabled="isEnable" @click="control('bathEnable')">启用</el-button>
        <el-button type="danger" plain :disabled="isUnEnable" @click="control('bathUnEnable')">停用 </el-button>
        <el-button type="danger" plain :disabled="!multipleSelection.length" @click="control('bathDel')">删除 </el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        height="calc(100% - 110px)"
        :data="tableData"
        border
        stripe
        table-layout="auto"
        class="tableAuto"
        row-key="id"
        @selection-change="selectionChange"
      >
        <el-table-column type="selection"> </el-table-column>
        <el-table-column label="仓库名称" prop="warehouseName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="管理部门" prop="manageUnitName" show-overflow-tooltip></el-table-column>
        <el-table-column label="库管员" prop="warehouseManagerName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="使用部门" prop="useUnitName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="状态" prop="status">
          <template #default="{ row }">
            <span v-if="row.status == '启用'" class="table-content__tag--1">{{ row.status }}</span>
            <span v-else class="table-content__tag--0">{{ row.status }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200px">
          <template #default="{ row }">
            <el-button type="text" @click="control('detail', row)"> 详情 </el-button>
            <el-button type="text" :disabled="row.status == '启用'" @click="control('edit', row)"> 编辑 </el-button>
            <el-dropdown @command="(command) => control(command, row)">
              <el-button type="text" style="margin-left: 10px">更多</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="'inventory'">查看库存 </el-dropdown-item>
                  <el-dropdown-item :command="'inventoryRecord'">出入库记录 </el-dropdown-item>
                  <el-dropdown-item :command="'changeState'" :class="[row.status == '启用' ? 'delete-item' : '']">
                    {{ row.status == '停用' ? '启用' : '停用' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="'del'" class="delete-item"> 删除 </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="warehouseManage-list__pagination"
        :current-page="pagination.current"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
      <!--仓库编辑-->
      <WarehouseEdit v-bind="dialogWarehouseEdit" :visible.sync="dialogWarehouseEdit.show" @success="getDataList" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin'
import { transData } from '@/util'
export default {
  name: 'warehouseManage',
  components: {
    WarehouseEdit: () => import('./components/warehouseEdit.vue')
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableLoading: false,
      searchForm: {
        keyWords: '',
        status: null,
        manageUnitId: '',
        useUnitId: ''
      },
      tableData: [],
      pagination: {
        current: 1,
        size: 15,
        total: 0
      },
      managerUnitOptions: [], // 管理部门
      useUnitOptions: [], // 使用部门
      // 仓库编辑弹窗
      dialogWarehouseEdit: {
        id: '',
        show: false
      },
      useUnitList: [],
      manageDeptList: [],
      isEnable: true, // 启用
      isUnEnable: true, // 禁用
      multipleSelection: [],
      // 状态选项
      statusOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '启用',
          value: '0'
        },
        {
          label: '停用',
          value: '1'
        }
      ],
      // 单位级联选择器配置
      manageUnitProps: {
        value: 'id',
        label: 'deptName',
        children: 'children',
        multiple: false,
        checkStrictly: true,
        emitPath: false
      },
      // 部门级联选择器配置
      useUnitProps: {
        value: 'id',
        label: 'deptName',
        children: 'children',
        multiple: false,
        checkStrictly: true,
        emitPath: false
      }
    }
  },
  mounted() {
    this.getDeptList()
    this.getDataList()
  },
  methods: {
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.useUnitOptions = transData(res.data, 'id', 'pid', 'children')
          this.managerUnitOptions = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    selectionChange(val) {
      this.multipleSelection = val
      if (this.multipleSelection.length && this.multipleSelection.length > 0) {
        let enableShow = this.multipleSelection.every((e) => e.status == '启用')
        let unEnableShow = this.multipleSelection.every((e) => e.status == '停用')
        if (enableShow) {
          this.isUnEnable = false
        } else {
          this.isUnEnable = true
        }
        if (unEnableShow) {
          this.isEnable = false
        } else {
          this.isEnable = true
        }
      } else {
        this.isUnEnable = true
        this.isEnable = true
      }
    },
    // 查询
    search() {
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      this.pagination.current = 1
      Object.assign(this.$data.searchForm, this.$options.data().searchForm)
      this.getDataList()
    },
    control(type, row) {
      if (['add', 'edit'].includes(type)) {
        // 添加 编辑
        this.dialogWarehouseEdit.id = row?.id || ''
        this.dialogWarehouseEdit.show = true
      } else if (['detail', 'inventory', 'inventoryRecord'].includes(type)) {
        // 详情
        let query = { id: row.id }
        this.$router.push({
          path: '/basicConfig/warehouseManage/warehouseDetail',
          query: {
            type,
            ...query
          }
        })
      } else if (type == 'del') {
        // 删除
        if (row.status == '启用') {
          this.$message.error('启用的仓库不允许删除！')
        } else {
          this.$confirm('删除后将无法恢复，是否删除？', '删除仓库', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.warehouseApi.delWarehouseInfo({ id: row.id }).then((res) => {
              if (res.code == 200 && res.data) {
                this.$message({ message: '仓库删除成功', type: 'success' })
                this.getDataList()
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          })
        }
      } else if (type === 'bathUnEnable') {
        this.bathEnableOrDisable('1')
      } else if (type === 'bathEnable') {
        this.bathEnableOrDisable('0')
      } else if (type == 'bathDel') {
        if (this.multipleSelection.some((item) => item.status != '停用')) {
          return this.$message.error('只能删除停用的仓库')
        }
        this.$confirm('删除后将无法恢复，是否删除？', '删除仓库', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'el-button--primary is-plain',
          type: 'warning'
        }).then(() => this.doBathDelete())
      } else if (type == 'changeState') {
        if (row.status == '启用') {
          this.$confirm('停用后将无法进行出/入库操作，是否停用？', '停用仓库', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            cancelButtonClass: 'el-button--primary is-plain',
            type: 'warning'
          }).then(() => this.doStatus(row))
        } else if (row.status == '停用') {
          this.doStatus(row)
        }
      }
    },
    // 启用停用一行
    doStatus(val) {
      this.tableLoadingStatus = true
      this.$api.warehouseApi
        .getWarehouseStatus({ id: val.id, status: val.status == '停用' ? '0' : '1' })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('变更状态成功')
            this.getDataList()
          } else {
            throw res.message || '变更状态失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 批量启用停用
    bathEnableOrDisable(val) {
      this.tableLoadingStatus = true
      let checkedIds = this.multipleSelection.map((it) => it.id)
      this.$api.warehouseApi
        .getWarehouseStatus({ id: checkedIds.join(','), status: val })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('变更状态成功')
            this.getDataList()
          } else {
            throw res.message || '变更状态失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 批量删除
    doBathDelete() {
      this.tableLoadingStatus = true
      let checkedIds = this.multipleSelection.map((it) => it.id)
      this.$api.warehouseApi
        .delWarehouseInfo({ id: checkedIds.join(',') })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已删除')
            this.getDataList()
          } else {
            throw res.message || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 获取仓库列表
    getDataList() {
      let param = {
        pageSize: this.pagination.size,
        CurrentPage: this.pagination.current,
        ...this.searchForm
      }
      this.tableLoading = true
      this.$api.warehouseApi
        .getWarehouseByPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pagination.total - deleteNum) / this.pagination.size)
      let currentPage = this.pagination.current > deleteAfterPage ? deleteAfterPage : this.pagination.current
      this.pagination.current = currentPage < 1 ? 1 : currentPage
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 16px !important;
  .search-from {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      align-items: center;
      & > div {
        margin-right: 10px;
      }
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  .el-table {
    margin: 16px 0;
  }
  &__tag {
    // 停用
    &--0 {
      color: #f64646;
    }
    // 启用
    &--1 {
      color: #00b42a;
    }
  }
}
// 下拉菜单中删除项的样式
::v-deep .delete-item {
  color: #f56c6c !important;
}
::v-deep .delete-item:hover {
  background-color: #fef0f0 !important;
  color: #f56c6c !important;
}
</style>
