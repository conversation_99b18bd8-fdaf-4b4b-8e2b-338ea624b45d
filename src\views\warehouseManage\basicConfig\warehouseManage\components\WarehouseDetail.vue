<template>
  <el-form ref="formRef" :model="formModel" label-width="120px">
    <el-row>
      <el-col :span="12">
        <el-form-item label="仓库名称">
          <span>{{ formModel.warehouseName || '' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="仓库编码">
          <span>{{ formModel.warehouseCode || '' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="仓库位置">
          <span>{{ formModel.warehouseAddress || '' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="管理部门">
          <span>{{ formModel.manageUnitName || '' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="库管员">
          <span>{{ formModel.warehouseManagerName || '' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="使用部门">
          <span>{{ formModel.useUnitName || '' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="仓库状态">
          <span>{{ formModel.status == '0' ? '启用' : '停用' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注">
          <span>{{ formModel.remarks || '' }}</span>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
export default {
  name: 'WarehouseDetail',
  props: {
    baseData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data: function () {
    return {
      // 正常表单
      formModel: {
        warehouseName: '',
        // 仓库名称
        warehouseCode: '',
        // 仓库编码
        warehouseAddress: '',
        // 仓库地址
        remarks: '',
        // 备注
        status: '0',
        // 管理部门
        manageUnitName: '',
        // 管理部门名称
        warehouseManagerName: '',
        // 仓库管理员name
        useUnitName: ''
        // 使用部门name
      }
    }
  },
  watch: {
    baseData: {
      handler(val) {
        this.formModel = val
      },
      deep: true
    }
  }
}
</script>
