<template>
  <el-dialog title="新增耗材" width="60%" :visible="visible" custom-class="model-dialog" :before-close="closeDialog">
    <div class="consumables_content">
      <div class="left">
        <div class="toptip">
          <div><span class="green_line"></span> 仓库</div>
        </div>
        <el-tree
          ref="treeRef"
          v-loading="treeLoadingStatus"
          node-key="id"
          :data="treeData"
          :current-node-key="currentKey"
          :expand-on-click-node="false"
          default-expand-all
          highlight-current
          :props="{ label: 'warehouseName' }"
          class="dictionary-list__tree"
          @node-click="onTreeNodeClick"
        ></el-tree>
      </div>
      <div class="right">
        <div class="header_operation">
          <div class="search_box">
            <div class="search_select">
              <el-input v-model.trim="filter.keyWords" placeholder="耗材编码/耗材名称"></el-input>
              <el-cascader
                v-model="filter.materialTypeCode"
                :options="consumablesTypeList"
                :props="materialTypeProps"
                clearable
                filterable
                size="mini"
                :show-all-levels="false"
                placeholder="耗材类型"
              ></el-cascader>
            </div>
          </div>
          <div class="header_btn">
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">搜索</el-button>
          </div>
        </div>
        <div class="table_div">
          <el-table
            ref="consumablesTable"
            v-loading="tableDataLoading"
            row-key="uniqueId"
            :data="tableData"
            style="width: 100%"
            height="calc(100% - 50px)"
            border
            @selection-change="selectionChange"
          >
            <el-table-column type="selection" :reserve-selection="true" width="55" align="center"> </el-table-column>
            <el-table-column label="序号" type="index" width="60">
              <template slot-scope="scope">
                <span>{{ (pageinationData.page - 1) * pageinationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="materialCode" label="耗材编码" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="materialName" label="耗材名称" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="materialTypeName" label="耗材类型" show-overflow-tooltip> </el-table-column>
            <el-table-column label="耗材类型" prop="materialTypeName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="规格型号" prop="model" show-overflow-tooltip> </el-table-column>
            <el-table-column label="计量单位" prop="basicUnitName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="品牌" prop="brandName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="单价" prop="unitPriceStr" show-overflow-tooltip> </el-table-column>
          </el-table>
          <el-pagination
            class="pagination"
            :current-page="pageinationData.page"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="pageinationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageinationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'selectConsumables',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    selectedData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      filter: {
        keyWords: '', // 名称/编码
        materialTypeCode: '' // 耗材类型
      },
      currentKey: '',
      tableDataLoading: false,
      treeData: [],
      treeLoadingStatus: false,
      tableData: [], // 数据
      pageinationData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      checkedData: {},
      systemCategoryOptions: [],
      multipleSelection: [],
      consumablesTypeList: [],
      materialTypeProps: {
        label: 'dictionaryDetailsName',
        children: 'children',
        value: 'id',
        multiple: true,
        checkStrictly: true,
        emitPath: false
      }
    }
  },
  mounted() {
    this.getConsumableTypeList()
    this.getTreeData()
  },
  methods: {
    // 查询耗材类型
    getConsumableTypeList() {
      this.$api.warehouseApi.getDictByPage({ dictionaryCategoryId: 'consumables_type', dictionaryDetailsStatus: '1' }).then((res) => {
        if (res.code == '200') {
          let allConsumablesTypes = transData(res.data, 'id', 'parentId', 'children')
          // 如果info.materialTypeCode有值，只显示这些值对应的类型
          if (this.info.materialTypeCode && this.info.materialTypeCode.length > 0) {
            // 只保留info.materialTypeCode中指定的耗材类型
            this.consumablesTypeList = this.filterConsumablesTypesToShow(allConsumablesTypes, this.info.materialTypeCode)
            // 设置默认选中为info.materialTypeCode中的值
            this.$set(this.filter, 'materialTypeCode', [])
          } else {
            this.consumablesTypeList = allConsumablesTypes
            this.$set(this.filter, 'materialTypeCode', [])
          }
        }
      })
    },
    // 只保留指定ID的耗材类型
    filterConsumablesTypesToShow(types, includeIds) {
      if (!types || !Array.isArray(types) || types.length === 0) {
        return []
      }
      return types.filter((type) => {
        // 递归检查当前节点及其子节点是否包含需要保留的ID
        return this.checkNodeAndChildrenForInclude(type, includeIds)
      })
    },
    // 检查节点及其子节点是否包含需要保留的ID
    checkNodeAndChildrenForInclude(node, includeIds) {
      // 检查当前节点是否需要保留
      if (includeIds.includes(node.id)) {
        return true
      }
      // 递归检查子节点
      if (node.children && node.children.length > 0) {
        for (let child of node.children) {
          if (this.checkNodeAndChildrenForInclude(child, includeIds)) {
            return true
          }
        }
      }
      return false
    },
    // 获取树数据
    getTreeData() {
      this.treeLoadingStatus = true
      let param = {
        status: '0'
      }
      this.$api.warehouseApi
        .getWarehouseByPage(param)
        .then((res) => {
          if (res.code == 200) {
            let list = res.data.list || []
            if (this.info.warehouseId && this.info.warehouseId.length) {
              // 如果有 warehouseId 数组，只保留对应的节点
              const matchedNodes = list.filter((item) => this.info.warehouseId.includes(item.id))
              if (matchedNodes.length > 0) {
                this.treeData = matchedNodes
                // 设置第一个匹配项为当前选中项
                this.currentKey = matchedNodes[0].id
                this.$nextTick(() => {
                  this.$refs.treeRef.setCurrentKey(this.currentKey)
                })
              } else {
                this.treeData = []
                this.currentKey = ''
              }
            } else {
              // 没有 warehouseId，正常显示树结构
              const root = { warehouseName: '全部', id: '', children: [] }
              root.children = list.map((it) => {
                it.parentId = root.id
                return it
              })
              this.treeData = [root]
              const first = root
              this.currentKey = first.id
              this.$nextTick(() => {
                this.$refs.treeRef.setCurrentKey(first.id)
              })
            }
          }
          this.onSearch()
          this.treeLoadingStatus = false
        })
        .catch((msg) => this.$message.error(msg || '获取仓库失败'))
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    // 当树点击被点击时
    onTreeNodeClick(data) {
      if (data.id !== this.currentKey) {
        this.currentKey = data.id
        this.onSearch()
        // 保持当前已选择的数据
        this.$nextTick(() => {
          if (this.multipleSelection && this.multipleSelection.length > 0) {
            // 重新选中之前选择的行
            this.multipleSelection.forEach((row) => {
              this.$refs.consumablesTable && this.$refs.consumablesTable.toggleRowSelection(row, true)
            })
          }
        })
      }
    },
    selectionChange(val) {
      this.multipleSelection = val
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pageinationData.page = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.currentKey = ''
      this.filter.materialTypeCode = ''
      this.filter.keyWords = ''
      this.$nextTick(() => {
        this.$refs.treeRef.setCurrentKey(this.currentKey)
      })
      this.onSearch()
    },
    // 获取列表
    getDataList() {
      // 合并 filter.materialTypeCode 和 info.materialTypeCode
      let materialTypeCodes = []
      // 添加用户选择的耗材类型
      if (this.filter.materialTypeCode && this.filter.materialTypeCode.length > 0) {
        materialTypeCodes = [...materialTypeCodes, ...this.filter.materialTypeCode]
      }
      // 添加已存在的耗材类型
      if (this.info.materialTypeCode && this.info.materialTypeCode.length > 0) {
        materialTypeCodes = [...materialTypeCodes, ...this.info.materialTypeCode]
      }
      // 去重并转换为逗号分隔的字符串
      const uniqueMaterialTypeCodes = [...new Set(materialTypeCodes)]
      const materialTypeCodeStr = uniqueMaterialTypeCodes.join(',')
      const params = {
        pageSize: this.pageinationData.pageSize,
        CurrentPage: this.pageinationData.page,
        keyWords: this.filter.keyWords,
        // 使用合并后的materialTypeCode
        materialTypeCode: materialTypeCodeStr,
        warehouseId: this.currentKey,
        status: '0', // 查询启用的耗材
        takeFlag: '1', // 是否是盘点
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      if (this.selectedData.length) {
        params.notAtIds = this.selectedData.map((item) => item.id).join(',')
      } else {
        params.notAtIds = ''
      }
      this.tableDataLoading = true
      this.$api.warehouseApi.getInventoryListByPage(params).then((res) => {
        this.tableDataLoading = false
        if (res.code == 200) {
          // 为每条数据生成唯一ID (warehouseId + id)
          this.tableData = res.data.list.map((item) => {
            return {
              ...item,
              takeStockNum: item.num,
              uniqueId: `${item.warehouseId || ''}_${item.id || ''}` // 生成唯一ID
            }
          })
          this.pageinationData.total = res.data.sum
          // 在数据加载完成后，恢复之前的选择状态
          this.$nextTick(() => {
            if (this.multipleSelection && this.multipleSelection.length > 0) {
              // 重新选中之前选择的行
              this.multipleSelection.forEach((row) => {
                // 查找新数据中对应的行（使用uniqueId匹配）
                const newRow = this.tableData.find((item) => `${item.warehouseId || ''}_${item.id || ''}` === row.uniqueId)
                if (newRow) {
                  this.$refs.consumablesTable && this.$refs.consumablesTable.toggleRowSelection(newRow, true)
                }
              })
            }
          })
        } else {
          this.$message.error(res.msg)
          this.tableData = []
        }
      })
    },
    // 弹框分页
    handleSizeChange(val) {
      this.pageinationData.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.pageinationData.page = val
      this.getDataList()
    },
    submitDialog() {
      if (this.multipleSelection.length > 0) {
        // 为选中的每一项添加完整的耗材类型路径名称
        const selectedData = this.multipleSelection.map((item) => {
          return { ...item, takeStockNum: item.num, item }
        })
        this.$emit('handleConsumablesSelect', selectedData)
      } else {
        this.$message.error('请先选择耗材')
      }
    },
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .consumables_content {
    padding: 0 16px;
    background: #fff;
    border-radius: 4px;
    display: flex;
    width: 100%;
    height: 500px;
    .left {
      width: 280px;
      border-right: solid 1px #eee;
      display: flex;
      flex-flow: column nowrap;
      &::v-deep(.el-tree) {
        height: calc(100% - 64px);
        overflow: auto;
        padding: 16px 0;
        .el-tree-node__content {
          line-height: 36px;
          height: 36px;
        }
        .el-tree-node.is-current > .el-tree-node__content {
          color: #3562db;
          background: #e6effc;
        }
      }
    }
    .right {
      flex: 1;
      overflow: hidden;
      padding: 16px;
      display: flex;
      height: 100%;
      flex-flow: column nowrap;
      .header_operation {
        margin-bottom: 10px;
        display: flex;
        .search_box {
          flex: 1;
          .search_select {
            margin-right: 16px;
          }
          .search_input {
            width: 200px;
          }
        }
      }
      .table_div {
        height: 100%;
      }
      .pagination {
        margin-top: 16px;
      }
    }
  }
}
.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 6px;
  border-radius: 2px !important;
  vertical-align: middle;
}
.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  padding-left: 16px;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}
.search_select {
  div {
    margin-right: 16px;
  }
  .el-input {
    width: 200px;
  }
}
</style>
