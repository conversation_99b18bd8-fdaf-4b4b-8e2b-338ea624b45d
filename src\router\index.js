import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store/index'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css' // progress bar style
import subsystemAuth from '@/util/subsystemAuth'
Vue.use(VueRouter)
import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
const constantRoutes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login'),
    meta: {
      title: '登录',
      i18n: 'route.login'
    }
  },
  {
    path: '/oauth',
    name: 'oauth',
    component: () => import('@/views/oauth'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('@/views/index'),
        meta: {
          title: '控制台',
          // title: store.state.settings.dashboardTitle,
          i18n: 'route.dashboard'
        }
      },
      // {
      //   path: 'personal',
      //   component: EmptyLayout,
      //   redirect: '/personal/setting',
      //   meta: {
      //     title: '个人中心',
      //     breadcrumb: false
      //   },
      //   children: [
      //     {
      //       path: 'setting',
      //       name: 'personalSetting',
      //       component: () => import('@/views/personal/setting'),
      //       meta: {
      //         title: '个人设置',
      //         i18n: 'route.personal.setting'
      //       }
      //     },
      //     {
      //       path: 'edit/password',
      //       name: 'personalEditPassword',
      //       component: () => import('@/views/personal/edit.password'),
      //       meta: {
      //         title: '修改密码',
      //         i18n: 'route.personal.editpassword'
      //       }
      //     }
      //   ]
      // },
      {
        path: 'reload',
        name: 'reload',
        component: () => import('@/views/reload')
      }
    ]
  }
]
import Drag from './modules/drag.js'
import Apply from './modules/apply.js'
import policymaking from './modules/policymaking.js'
import AlarmCenter from './modules/alarmCenter.js'
import WorkOrder from './modules/workOrder.js'
import Management from './modules/management.js'
import Equipment from './modules/Equipment.js'
import Monitor from './modules/monitor.js'
import Energy from './modules/energy.js'
import serviceQuality from './modules/serviceQuality.js'
import inspection from './modules/inspection.js'
import securityCenter from './modules/securityCenter.js'
import foundation from './modules/foundation.js'
import operationPort from './modules/operationPort.js'
import basicInformation from './modules/basicInformation.js'
import space from './modules/space.js'
import targetAnalysis from './modules/targetAnalysis.js'
import rentalHousing from './modules/rentalHousing.js'
import Propagandaeducation from './modules/propagandaeducation.js'
import construction from './modules/construction.js'
import warehouseManage from './modules/warehouseManage.js'
import oneStop from './modules/oneStop.js'
// 当 children 不为空的主导航只有一项时，则隐藏
// menuAuth: '/auth' 用于权限菜单控制
// auth: ['userManagement:add'] 用于按钮权限控制
let asyncRoutes = [
  {
    meta: {
      title: '待办',
      menuAuth: '/wait',
      fullTitle: '待办事项',
      pageJump: true
    },
    children: Drag
  },
  {
    meta: {
      title: '报警',
      menuAuth: '/alarmCenter',
      fullTitle: '报警中心'
    },
    children: AlarmCenter
  },
  {
    meta: {
      title: '工单',
      menuAuth: '/workOrder',
      fullTitle: '工单管理'
    },
    children: WorkOrder
  },
  {
    meta: {
      title: '设备',
      menuAuth: '/equipment',
      fullTitle: '设施设备管理'
    },
    children: Equipment
  },
  {
    meta: {
      title: '巡检',
      menuAuth: '/inspection',
      fullTitle: '综合服务巡检'
    },
    children: inspection
  },
  {
    meta: {
      title: '监测',
      menuAuth: '/monitor',
      fullTitle: '运行监控'
    },
    children: Monitor
  },
  {
    meta: {
      title: '能耗',
      menuAuth: '/energy',
      fullTitle: '能耗分析'
    },
    children: Energy
  },
  {
    meta: {
      title: '服务',
      menuAuth: '/serviceQuality',
      fullTitle: '服务品质管理'
    },
    children: serviceQuality
  },
  {
    meta: {
      title: '公租房',
      menuAuth: '/rentalHousing',
      fullTitle: '公租房管理'
    },
    children: rentalHousing
  },
  // {
  //   meta: {
  //     title: '基础',
  //     menuAuth: '/foundation',
  //     fullTitle: '基础档案管理'
  //   },
  //   children: foundation
  // },
  {
    meta: {
      title: '安全',
      menuAuth: '/securityCenter',
      fullTitle: '双预防管理',
      // type: 'ipsm',
      sysType: 'ipsm'
    },
    children: securityCenter
  },
  {
    meta: {
      title: '应用',
      menuAuth: '/applyCenter',
      fullTitle: '应用中心',
      pageJump: true
    },
    children: Apply
  },
  {
    meta: {
      title: '指标',
      menuAuth: '/policymaking',
      fullTitle: '指标管理'
    },
    children: policymaking
  },
  {
    meta: {
      title: '指标分析',
      menuAuth: '/targetAnalysis',
      fullTitle: '指标分析'
    },
    children: targetAnalysis
  },
  {
    meta: {
      title: '宣教',
      menuAuth: '/propagandaeducation',
      fullTitle: '宣教'
    },
    children: Propagandaeducation
  },
  {
    meta: {
      title: '系统',
      menuAuth: '/system',
      fullTitle: '系统管理'
    },
    children: Management
  },
  {
    meta: {
      title: '空间',
      menuAuth: '/space',
      fullTitle: '空间管理'
    },
    children: space
  },
  {
    meta: {
      title: '运营',
      menuAuth: '/operationPort',
      fullTitle: '运营支撑管理'
    },
    children: operationPort
  },
  {
    meta: {
      title: '基础',
      menuAuth: '/basicInformation',
      fullTitle: '基础信息'
    },
    children: basicInformation
  },
  {
    meta: {
      title: '施工',
      menuAuth: '/construction',
      fullTitle: '施工作业'
    },
    children: construction
  },
  {
    meta: {
      title: '库房',
      menuAuth: '/warehouseManage',
      fullTitle: '库房管理'
    },
    children: warehouseManage
  },
  {
    meta: {
      title: '一站式',
      menuAuth: '/oneStop',
      fullTitle: '一站式服务'
    },
    children: oneStop
  }
]
const lastRoute = [
  {
    path: '*',
    component: () => import('@/views/404'),
    meta: {
      title: '404',
      sidebar: false
    }
  }
]
const router = new VueRouter({
  mode: 'history',
  routes: constantRoutes,
  base: __PATH.BASE_URL
})
// 解决路由在 push/replace 了相同地址报错的问题
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err)
}
const originalReplace = VueRouter.prototype.replace
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err) => err)
}
router.beforeEach(async (to, from, next) => {
  store.state.settings.enableProgress && NProgress.start()
  // 已经登录，但还没根据权限动态生成并挂载路由
  if (store.getters['user/isLogin'] && !store.state.menu.isGenerate) {
    // 挂载动态路由的同时，根据当前帐号复原固定标签栏
    store.state.settings.enableTabbar && store.commit('tabbar/recoveryStorage', store.state.user.userInfo.username)
    router.matcher = new VueRouter({
      routes: constantRoutes
    }).matcher
    const accessRoutes = await store.dispatch('menu/generateRoutes', {
      asyncRoutes,
      currentPath: to.path
    })
    accessRoutes.push(...lastRoute)
    accessRoutes.forEach((route) => {
      router.addRoute(route)
    })
    let newObj = {}
    router.getRoutes().forEach((v) => (newObj[v.path] = v.meta.sysType))
    store.commit('menu/setSubsystemAuth', newObj)
    next({ ...to, replace: true })
  }
  if (store.state.menu.isGenerate) {
    store.commit('menu/setHeaderActived', to.path)
  }
  if (store.getters['user/isLogin']) {
    if (to.name) {
      if (to.matched.length !== 0) {
        // 如果已登录状态下，进入登录页会强制跳转到控制台页面
        if (to.name == 'login') {
          next({
            name: 'dashboard',
            replace: true
          })
        } else if (!store.state.settings.enableDashboard && to.name == 'dashboard') {
          // 如果未开启控制台页面，则默认进入第一个固定标签栏或者侧边栏导航第一个模块
          if (store.state.settings.enableTabbar && store.state.tabbar.list.length > 0) {
            next({
              path: store.state.tabbar.list[0].path,
              replace: true
            })
          } else if (store.getters['menu/sidebarRoutes'].length > 0) {
            next({
              path: store.getters['menu/sidebarRoutes'][0].path,
              replace: true
            })
          }
        }
      } else {
        // 如果是通过 name 跳转，并且 name 对应的路由没有权限时，需要做这步处理，手动指向到 404 页面
        next({
          path: '/404'
        })
      }
    }
  } else {
    if (to.name === 'oauth') {
      next()
    } else if (to.name != 'login') {
      next({
        name: 'login',
        query: {
          redirect: to.fullPath
        }
      })
    }
  }
  // 子模块权限验证
  const someRoleVailable = store.state.menu.subsystemAuth[to.path]
  if (someRoleVailable) {
    subsystemAuth[someRoleVailable](to, from, next)
  } else {
    next()
  }
})
router.afterEach(() => {
  store.state.settings.enableProgress && NProgress.done()
})
export default router
