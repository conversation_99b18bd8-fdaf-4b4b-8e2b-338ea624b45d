import Vue from 'vue'

// 添加通用样式
const addGlobalStyles = () => {
  if (document.getElementById('dialog-resize-styles')) return

  const style = document.createElement('style')
  style.id = 'dialog-resize-styles'
  style.textContent = `
    .dialog-resized .el-dialog__body {
      max-height: none !important;
      height: calc(100% - 110px) !important;
      overflow: auto !important;
    }

    .dialog-resized.is-fullscreen .el-dialog__body {
      height: calc(100% - 110px) !important;
    }
  `
  document.head.appendChild(style)
}

// 初始化时添加样式
addGlobalStyles()

// v-dialogResize: 弹窗右下角拖拽放大缩小指令
Vue.directive('dialogResize', {
  bind(el, binding, vnode, oldVnode) {
    const dragDom = el.querySelector('.el-dialog')

    if (!dragDom) {
      console.warn('v-dialogResize: 未找到 .el-dialog 元素')
      return
    }

    // 创建拖拽手柄
    const resizeHandle = document.createElement('div')
    resizeHandle.className = 'dialog-resize-handle'
    resizeHandle.style.cssText = `
      position: absolute;
      right: 0;
      bottom: 0;
      width: 20px;
      height: 20px;
      cursor: nw-resize;
      background: transparent;
      z-index: 9999;
      user-select: none;
    `

    // 创建拖拽图标
    const resizeIcon = document.createElement('div')
    resizeIcon.className = 'dialog-resize-icon'
    resizeIcon.style.cssText = `
      position: absolute;
      right: 3px;
      bottom: 3px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-bottom: 8px solid #ccc;
      cursor: nw-resize;
      transition: border-bottom-color 0.2s ease;
    `

    resizeHandle.appendChild(resizeIcon)
    dragDom.appendChild(resizeHandle)

    // 设置对话框相对定位
    if (dragDom.style.position !== 'absolute' && dragDom.style.position !== 'fixed') {
      dragDom.style.position = 'relative'
    }

    let isResizing = false
    let hasStartedResizing = false // 标记是否已经开始实际拖拽
    let startX, startY, startWidth, startHeight

    // 获取配置选项
    const options = binding.value || {}
    const minWidth = options.minWidth || 300
    const minHeight = options.minHeight || 200
    const maxWidth = options.maxWidth || window.innerWidth * 0.9
    const maxHeight = options.maxHeight || window.innerHeight * 0.9
    const onResize = options.onResize || (() => {})

    resizeHandle.onmousedown = (e) => {
      e.preventDefault()
      e.stopPropagation()

      isResizing = true
      startX = e.clientX
      startY = e.clientY

      // 获取当前尺寸
      const computedStyle = window.getComputedStyle(dragDom)
      startWidth = parseInt(computedStyle.width, 10)
      startHeight = parseInt(computedStyle.height, 10)

      // 添加选择禁用样式
      document.body.style.userSelect = 'none'
      document.body.style.cursor = 'nw-resize'

      // 添加遮罩层防止iframe干扰
      const mask = document.createElement('div')
      mask.className = 'dialog-resize-mask'
      mask.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9998;
        cursor: nw-resize;
      `
      document.body.appendChild(mask)

      document.onmousemove = (e) => {
        if (!isResizing) return

        const deltaX = e.clientX - startX
        const deltaY = e.clientY - startY

        // 只有在实际开始拖拽时才添加样式类（避免闪烁）
        if (!hasStartedResizing && (Math.abs(deltaX) > 2 || Math.abs(deltaY) > 2)) {
          hasStartedResizing = true
          el.classList.add('dialog-resized')
        }

        let newWidth = Math.max(minWidth, Math.min(maxWidth, startWidth + deltaX))
        let newHeight = Math.max(minHeight, Math.min(maxHeight, startHeight + deltaY))

        dragDom.style.width = `${newWidth}px`
        dragDom.style.height = `${newHeight}px`

        // 触发回调函数
        onResize({ width: newWidth, height: newHeight })
      }

      document.onmouseup = () => {
        isResizing = false
        hasStartedResizing = false // 重置拖拽状态
        document.onmousemove = null
        document.onmouseup = null

        // 恢复样式
        document.body.style.userSelect = ''
        document.body.style.cursor = ''

        // 移除遮罩层
        const mask = document.querySelector('.dialog-resize-mask')
        if (mask) {
          document.body.removeChild(mask)
        }
      }
    }

    // 鼠标悬停效果
    resizeHandle.onmouseenter = () => {
      resizeIcon.style.borderBottomColor = '#409eff'
    }

    resizeHandle.onmouseleave = () => {
      resizeIcon.style.borderBottomColor = '#ccc'
    }

    // 存储手柄引用，用于清理
    el._resizeHandle = resizeHandle
  },

  update(el, binding) {
    // 当绑定值更新时，更新配置
    const resizeHandle = el._resizeHandle
    if (resizeHandle && binding.value !== binding.oldValue) {
      // 可以在这里更新配置
    }
  },

  unbind(el) {
    // 清理拖拽手柄
    const resizeHandle = el._resizeHandle
    if (resizeHandle && resizeHandle.parentNode) {
      resizeHandle.parentNode.removeChild(resizeHandle)
    }

    // 清理可能残留的遮罩层
    const mask = document.querySelector('.dialog-resize-mask')
    if (mask) {
      document.body.removeChild(mask)
    }

    // 恢复样式
    document.body.style.userSelect = ''
    document.body.style.cursor = ''

    // 清理拖拽放大的样式类
    el.classList.remove('dialog-resized')

    delete el._resizeHandle
  }
})

// 导出指令，方便单独使用
export default {
  name: 'dialogResize',
  directive: Vue.directive('dialogResize')
}
