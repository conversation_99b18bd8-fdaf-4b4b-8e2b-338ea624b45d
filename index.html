<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="keywords" content="后台系统,管理后台" />
    <meta name="description" content="智慧医院后勤综合监管平台" />
    <title><%= VUE_APP_TITLE %></title>
    <!-- 通用图标 -->
    <link href="/meta2dStatic/icon/GenericIcon/iconfont.css" rel="stylesheet" />
    <!-- meta2d-2d -->
    <link href="/meta2dStatic/icon/2d/iconfont.css" rel="stylesheet" />
    <!-- 国家电网 -->
    <link href="/meta2dStatic/icon/StateGrid/iconfont.css" rel="stylesheet" />
    <!-- 电气 -->
    <link href="/meta2dStatic/icon/lectricalEngineering/iconfont.css" rel="stylesheet" />
    <style>
      .back-stage-home {
        position: absolute;
        z-index: 10000;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        user-select: none;
        color: #736477;
        background-color: snow;
      }
      .back-stage-home .loading {
        position: relative;
        display: inline-block;
        width: 1em;
        height: 1em;
        color: inherit;
        vertical-align: middle;
        pointer-events: none;
        transform: scale(3);
      }
      .back-stage-home .loading::before,
      .back-stage-home .loading::after {
        content: '';
        display: block;
        position: absolute;
        background-color: currentColor;
        left: 50%;
        right: 0;
        top: 0;
        bottom: 50%;
        box-shadow: -0.5em 0 0 currentColor;
        animation: loading 1s linear infinite;
      }
      .back-stage-home .loading::after {
        top: 50%;
        bottom: 0;
        animation-delay: 0.25s;
      }
      @keyframes loading {
        0%,
        100% {
          box-shadow: -0.5em 0 0 transparent;
          background-color: currentColor;
        }
        50% {
          box-shadow: -0.5em 0 0 currentColor;
          background-color: transparent;
        }
      }
      .back-stage-home .text {
        font-size: 24px;
        margin-top: 50px;
      }
    </style>
    <script>
      window.jsDiagramNum = 613 //1450
      //电力 749
      //物联网 613
      //其他 88
      //所有 1450
      window.companyName = ''
      window.userDefinedDiagram = null
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= VUE_APP_TITLE %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <div class="back-stage-home">
        <div class="loading"></div>
        <div class="text"><%= VUE_APP_TITLE %>载入中…</div>
      </div>
    </div>
    <script defer src="/meta2dStatic/diagram/964909.1.js"></script>
    <script defer src="/meta2dStatic/diagram/964909.2.js"></script>
    <script defer src="/meta2dStatic/diagram/964909.3.js"></script>
    <script defer src="/meta2dStatic/diagram/964909.4.js"></script>
    <script defer src="/meta2dStatic/diagram/964909.5.js"></script>
    <script defer src="/meta2dStatic/diagram/964909.6.js"></script>
    <script defer src="/meta2dStatic/diagram/964909.7.js"></script>
    <script defer src="/meta2dStatic/diagram/964909.8.js"></script>
    <script defer src="/meta2dStatic/diagram/964909.9.js"></script>
    <script src="/meta2dStatic/diagram/rg.js"></script>
    <script type="module" src="/src/main.js"></script>
    <script>
      var intAccount = {
        username: 'gy_energy',
        password: 'Gy12345678!1'
      }
      var oAuthData = {
        clientId: 'szzlyy',
        clientSecret: '660a2957'
      }
      // 世纪坛
      var energyEmodelId = '1574997196833566721'
      if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'szzlyy' || '<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'sinomis' || '<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'outernet') {
        intAccount = {
          username: 'gy_function',
          password: 'Xm12345678!'
        }
        energyEmodelId = '1724753192417259521'
      } else if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'tjzlyy') {
        // 天津肿瘤医院
        intAccount = {
          username: 'gy_function',
          password: 'Xm12345678!'
        }
        energyEmodelId = '1574997196057620481'
      } else if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'ljxyy') {
        oAuthData = {
          clientId: 'ljxrmyy',
          clientSecret: '4498093c'
        }
        intAccount = {
          username: 'admin',
          password: 'Es12345678!'
        }
        energyEmodelId = '1767475808307785729'
      } else if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'bjsjtyy') {
        // 北京世纪坛医院
        intAccount = {
          username: 'technician',
          password: 'Gy12345678!'
        }
        energyEmodelId = '1574997196833566721'
      } else if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'fjslyy') {
        // 福建省立医院
        intAccount = {
          username: 'gy_function',
          password: 'Xm12345678!'
        }
        energyEmodelId = '1780885716285796354'
      }
      var __PATH = {
        VUE_SPORADIC_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-sporadic/', // sporadic
        VUE_IEMC_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-aircondition/', // iemc
        VUE_IEMC_ELEVATOR_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'iemc_medical_elevator/', // iemc电梯
        VUE_SYS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-authweb/', // 系统管理
        VUE_WARN_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-alarm/', // 报警
        VUE_ICIS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'inspection/', // 巡检
        // VUE_ICIS_API: 'http://*************:8198/', // 巡检
        VUE_IOMS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'oneLogistics/', // 一站式
        VUE_AQ_URL: '<%= VUE_APP_UNIFIED_SERVER %>' + 'ipsm/', //安全
        VUE_APP_IMWS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'medicalWaste/', // 医废
        VUE_SPACE_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'base_info/', // 基础信息
        VUE_SPACE_FILE: '<%= VUE_APP_UNIFIED_SERVER %>' + 'base_info_file/', // 基础信息上传
        VUE_ICIS_LZP: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-service-external/', // 网关基础服务
        VUE_NEWS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'service-message/', // 消息公告
        VUE_APP_PARTS_LIBRARY: '<%= VUE_APP_UNIFIED_SERVER %>' + 'chemical/', // 配件管理
        VUE_MINIO_API: '<%= VUE_APP_MINIO_SERVER %>', // minio
        DICTIONAR_URL: '<%= VUE_APP_DICTIONAR_SERVER %>', // iemc字典
        WS_ALARM_URL: '<%= VUE_APP_WS_SERVER %>' + 'websocket/', // 报警websocket
        WS_IEMC_URL: '<%= VUE_APP_WS_SERVER %>' + 'iemcWebsocket/', // iemc websocket
        WS_ELEVATOR_URL: '<%= VUE_APP_WS_SERVER %>' + 'iemcEleWebsocket/', // iemcElv websocket
        WS_IOMS_URL: '<%= VUE_APP_WS_SERVER %>' + 'iomsWebsocket/', // 待办事项websocket
        WS_NEWS_API: '<%= VUE_APP_NEWS_WS_SERVER %>', // 消息websocket
        VUE_APP_RTSP_LIVE_WS_SERVER: '<%= VUE_APP_RTSP_LIVE_WS_SERVER %>', // rtsp websocket
        VUE_APP_HOSPITAL_NODE_ENV: '<%= VUE_APP_HOSPITAL_NODE_ENV %>', // 对应医院环境 sinomis/bjsjtyy/gqgjzyy/jxsdeyy/bjsyzyyy/ljxyy
        VUE_ENERGY_API: '<%= VUE_APP_ENERGY_API %>' + 'api/', // 能耗接口地址
        SPACE_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-service-manage/', // 空间
        VUE_APP_IEMS_IOMS_API: '<%= VUE_APP_IEMS_IOMS_API %>', // 资产一站式
        VUE_APP_IEMS_API: '<%= VUE_APP_IEMS_API %>', // 资产
        VUE_PLAN_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'preplan/', // 演习预案
        VUE_RHMS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-rental-housing/', //公租房
        VUE_CONVERGED_COM_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-service-internet/', // 融合通信
        VUE_COURSE_URL: '<%= VUE_APP_UNIFIED_SERVER %>' + 'courseApi/', // 安防-培训
        BASE_URL: '<%= VUE_APP_BASE_URL_SUFFIX %>', // 路由配置二级地址
        PROJECT_VERSION: 'V3.3.LO.ISS', // 软件版本号
        PREVIEW_URL: '<%= VUE_APP_UNIFIED_SERVER %>' + "/preview/onlinePreview?url=", // 实验室course文档查看
        BASE_URL_LABORATORY: '<%= VUE_APP_UNIFIED_SERVER %>' + 'courseApi/', // 安防实验室课程 （本地调式专用）
        BASE_URL_LAB: '<%= VUE_APP_UNIFIED_SERVER %>' + 'labApi/', // 安防实验室 （本地调式专用）
        BASE_URL_HSC: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-chemicalWeb/' // 危化品
      }
    </script>
  </body>
</html>
