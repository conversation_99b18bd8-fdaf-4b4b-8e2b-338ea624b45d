<template>
  <el-form ref="formRef" class="warehouse-form" :model="formModel" :rules="rules" :disabled="readonly" :class="{ readonly }" label-width="120px">
    <el-row>
      <el-col :span="12">
        <el-form-item label="仓库名称" prop="warehouseName">
          <el-input v-model="formModel.warehouseName" placeholder="请输入" clearable maxlength="50" show-word-limit></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="仓库编码" prop="warehouseCode">
          <el-input v-model="formModel.warehouseCode" placeholder="请输入" clearable maxlength="30" :disabled="formModel.id" show-word-limit></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="仓库位置" prop="warehouseAddress">
          <el-input v-model="formModel.warehouseAddress" placeholder="请输入" clearable maxlength="100" show-word-limit></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="管理部门" prop="manageUnitId">
          <el-cascader
            ref="manageUnitCascader"
            v-model="formModel.manageUnitId"
            placeholder="请选择"
            :options="managerUnitOptions"
            :props="manageUnitProps"
            :show-all-levels="false"
            clearable
            filterable
            @change="selectManageUnit"
          >
          </el-cascader>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="库管员" prop="warehouseManagerId">
          <el-select v-model="formModel.warehouseManagerId" filterable clearable placeholder="请选择" @change="selectManagerPerson">
            <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="使用部门" prop="useUnitId">
          <el-cascader
            ref="useDeptCascader"
            v-model="formModel.useUnitId"
            placeholder="请选择"
            :options="useUnitOptions"
            :props="useUnitProps"
            :show-all-levels="false"
            clearable
            filterable
            @change="selectUseUnit"
          >
          </el-cascader>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="仓库状态" prop="status">
          <el-radio-group v-model="formModel.status">
            <el-radio :label="'0'">启用</el-radio>
            <el-radio :label="'1'">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="formModel.remarks" type="textarea" :rows="3" clearable maxlength="200" placeholder="请输入" show-word-limit></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'WarehouseForm',
  props: {
    readonly: Boolean,
    large: Boolean
  },
  data: function () {
    return {
      // 正常表单
      formModel: {
        warehouseName: '',
        // 仓库名称
        warehouseCode: '',
        // 仓库编码
        warehouseAddress: '',
        // 仓库地址
        remarks: '',
        // 备注
        status: '0',
        // 仓库状态
        manageUnitId: [],
        // 管理部门
        manageUnitName: '',
        // 管理部门名称
        warehouseManagerId: '',
        // 仓库管理员id
        warehouseManagerName: '',
        // 仓库管理员name
        useUnitId: [],
        // 使用部门id
        useUnitName: ''
        // 使用部门name
      },
      managerUnitOptions: [], // 管理部门
      useUnitOptions: [], // 使用部门
      personList: [], // 人员
      rules: {
        warehouseName: [{ required: true, message: '请输入仓库名称' }],
        warehouseCode: [{ required: true, message: '请输入仓库编码' }],
        manageUnitId: [{ required: true, message: '请选择管理部门' }],
        warehouseManagerId: [{ required: true, message: '请选择库管员' }]
      },
      // 计时器ID
      $timerId: -1,
      // 单位级联选择器配置
      manageUnitProps: {
        value: 'id',
        label: 'deptName',
        children: 'children',
        multiple: false,
        checkStrictly: true
      },
      // 部门级联选择器配置
      useUnitProps: {
        value: 'id',
        label: 'deptName',
        children: 'children',
        multiple: true,
        checkStrictly: true,
        emitPath: false
      }
    }
  },
  computed: {},
  created() {
    this.getDeptList()
  },
  beforeDestroy() {
    clearTimeout(this.$timerId)
  },
  methods: {
    // 获取部门列表
    getDeptList() {
      return new Promise((resolve) => {
        this.$api
          .getDeptList()
          .then((res) => {
            if (res.code == 200) {
              this.useUnitOptions = this.transformDeptData(res.data, 'id', 'pid', 'children')
              this.managerUnitOptions = this.transformDeptData(res.data, 'id', 'pid', 'children')
            }
            resolve()
          })
          .catch(() => {
            resolve() // 即使失败也要resolve
          })
      })
    },
    // 部门数据转换为树形结构
    transformDeptData(data, id, pid, children) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('部门数据为空或格式不正确')
        return []
      }
      const res = []
      const temp = {}
      try {
        // 第一次遍历，建立id到节点的映射
        data.forEach((item) => {
          if (item && item[id]) {
            temp[item[id]] = { ...item }
          }
        })
        // 第二次遍历，建立父子关系
        data.forEach((item) => {
          if (!item) return
          const itemId = item[id]
          const tempPid = item[pid]
          // 检查是否有有效的父ID，且父ID存在于数据中
          if (tempPid && tempPid !== '0' && tempPid !== itemId && temp[tempPid]) {
            if (!temp[tempPid][children]) {
              temp[tempPid][children] = []
            }
            temp[tempPid][children].push(temp[itemId])
          } else {
            // 根节点或父节点不存在的情况
            if (temp[itemId]) {
              res.push(temp[itemId])
            }
          }
        })
        return res
      } catch (error) {
        return []
      }
    },
    // 选择管理部门
    selectManageUnit(val) {
      if (val && val.length > 0) {
        // 获取选中的节点路径名称
        const pathLabels = val.map((id) => this.findNodeLabel(this.managerUnitOptions, id)).filter(Boolean)
        this.formModel.manageUnitName = pathLabels.join('/')
        // 获取人员列表
        this.getLersonnelList([val[val.length - 1]])
      } else {
        this.formModel.manageUnitName = ''
        this.personList = []
        this.formModel.warehouseManagerId = ''
        this.formModel.warehouseManagerName = ''
      }
    },
    findNodeLabel(options, value) {
      for (const node of options) {
        if (node.id === value) {
          return node.deptName
        }
        if (node.children && node.children.length > 0) {
          const result = this.findNodeLabel(node.children, value)
          if (result) return result
        }
      }
      return null
    },
    // 使用部门
    selectUseUnit(val) {
      if (Array.isArray(val) && val.length > 0) {
        // 获取每个选中节点的完整路径
        const fullPathNames = val
          .map((id) => {
            // 获取该节点的完整路径
            return this.getNodeFullPath(this.useUnitOptions, id)
          })
          .filter((path) => path !== null) // 过滤掉未找到的节点
        // 用逗号拼接所有路径
        this.formModel.useUnitName = fullPathNames.join(',')
      } else {
        this.formModel.useUnitName = ''
      }
    },
    // 获取节点的完整路径
    getNodeFullPath(options, targetId, currentPath = []) {
      for (const node of options) {
        // 创建当前路径副本
        const newPath = [...currentPath, node.deptName]
        if (node.id === targetId) {
          // 找到目标节点，返回完整路径
          return newPath.join('/')
        }
        // 递归查找子节点
        if (node.children && node.children.length > 0) {
          const result = this.getNodeFullPath(node.children, targetId, newPath)
          if (result) {
            return result
          }
        }
      }
      return null
    },
    // 获取人员列表
    getLersonnelList(deptIds) {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: deptIds.join(',')
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
        }
      })
    },
    // 人员获取name
    selectManagerPerson(id) {
      const selected = this.personList.find((item) => item.id === id)
      if (selected) {
        this.formModel.warehouseManagerName = selected.staffName
      }
    },
    /**
     * 反显数据
     * expose
     */
    // 获取详情信息
    revertData(data) {
      if (!data) return
      // 获取详情信息
      this.formModel.warehouseName = data.warehouseName || ''
      this.formModel.warehouseCode = data.warehouseCode || ''
      this.formModel.warehouseAddress = data.warehouseAddress || ''
      this.formModel.warehouseManagerId = data.warehouseManagerId || ''
      this.formModel.warehouseManagerName = data.warehouseManagerName || ''
      this.formModel.manageUnitName = data.manageUnitName || ''
      this.formModel.useUnitName = data.useUnitName || ''
      this.formModel.remarks = data.remarks || ''
      this.formModel.status = data.status || '0'
      this.formModel.id = data.id || ''
      this.formModel.manageUnitId = data.manageUnitId.split(',') || []
      this.getLersonnelList(this.formModel.manageUnitId)
      if (data.useUnitId) {
        this.formModel.useUnitId = data.useUnitId.split(',')
      } else {
        this.formModel.useUnitId = []
      }
      this.$refs.formRef.clearValidate()
    },
    /**
     * 重置表单数据
     * expose
     */
    resetFields() {
      clearTimeout(this.$timerId)
      this.$refs.formRef.resetFields()
    },
    /**
     * expose
     * 验证表单数据，并返回数据
     * @return { Promise<Object> } 表单数据
     */
    validate() {
      return this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          const params = {
            // 库房名称
            warehouseName: this.formModel.warehouseName,
            // 库房编码
            warehouseCode: this.formModel.warehouseCode,
            // 仓库位置
            warehouseAddress: this.formModel.warehouseAddress,
            // 仓库状态
            status: this.formModel.status,
            // 使用部门名称
            useUnitName: this.formModel.useUnitName,
            // 库管员id
            warehouseManagerId: this.formModel.warehouseManagerId,
            // 库管员Name
            warehouseManagerName: this.formModel.warehouseManagerName,
            // 管理部门名称
            manageUnitName: this.formModel.manageUnitName,
            // 备注信息
            remarks: this.formModel.remarks
          }
          if (Array.isArray(this.formModel.manageUnitId)) {
            params.manageUnitId = this.formModel.manageUnitId.join(',')
          }
          if (Array.isArray(this.formModel.useUnitId)) {
            params.useUnitId = this.formModel.useUnitId.join(',')
          }
          return params
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form {
  width: 100%;
  background-color: #fff;
  padding: 10px 16px 0;
  height: 100%;
  .el-cascader,
  .el-select {
    width: 100%;
  }
}
</style>
